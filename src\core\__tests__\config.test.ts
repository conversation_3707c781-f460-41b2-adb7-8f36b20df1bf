import { ConfigManager } from '../config';
import { ConfigError } from '@/types';

describe('ConfigManager', () => {
  let configManager: ConfigManager;

  beforeEach(() => {
    // Reset singleton instance for each test
    (ConfigManager as any).instance = undefined;
    configManager = ConfigManager.getInstance();
  });

  afterEach(() => {
    // Clean up
    configManager.reset();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = ConfigManager.getInstance();
      const instance2 = ConfigManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('get and set', () => {
    it('should set and get configuration values', () => {
      configManager.set('provider', 'deepseek');
      expect(configManager.get('provider')).toBe('deepseek');
    });

    it('should throw error for invalid values', () => {
      expect(() => {
        configManager.set('temperature', 3.0); // Invalid temperature > 2
      }).toThrow(ConfigError);
    });

    it('should handle boolean values', () => {
      configManager.set('autoApprove', true);
      expect(configManager.get('autoApprove')).toBe(true);
    });

    it('should handle number values', () => {
      configManager.set('maxTokens', 2048);
      expect(configManager.get('maxTokens')).toBe(2048);
    });
  });

  describe('getAll and setAll', () => {
    it('should get all configuration', () => {
      const config = configManager.getAll();
      expect(config).toHaveProperty('provider');
      expect(config).toHaveProperty('model');
      expect(config).toHaveProperty('maxTokens');
    });

    it('should set multiple configuration values', () => {
      const newConfig = {
        provider: 'ollama' as const,
        model: 'llama2',
        maxTokens: 1024
      };

      configManager.setAll(newConfig);
      
      expect(configManager.get('provider')).toBe('ollama');
      expect(configManager.get('model')).toBe('llama2');
      expect(configManager.get('maxTokens')).toBe(1024);
    });

    it('should validate configuration when setting all', () => {
      const invalidConfig = {
        provider: 'invalid-provider' as any,
        temperature: 5.0 // Invalid temperature
      };

      expect(() => {
        configManager.setAll(invalidConfig);
      }).toThrow(ConfigError);
    });
  });

  describe('has and delete', () => {
    it('should check if configuration key exists', () => {
      configManager.set('apiKey', 'test-key');
      expect(configManager.has('apiKey')).toBe(true);
      expect(configManager.has('nonexistent' as any)).toBe(false);
    });

    it('should delete configuration key', () => {
      configManager.set('apiKey', 'test-key');
      expect(configManager.has('apiKey')).toBe(true);
      
      configManager.delete('apiKey');
      expect(configManager.has('apiKey')).toBe(false);
    });
  });

  describe('isConfigured', () => {
    it('should return false when not configured', () => {
      expect(configManager.isConfigured()).toBe(false);
    });

    it('should return true when deepseek is properly configured', () => {
      configManager.setAll({
        provider: 'deepseek',
        model: 'deepseek-chat',
        apiKey: 'sk-test-key'
      });
      
      expect(configManager.isConfigured()).toBe(true);
    });

    it('should return true when ollama is properly configured', () => {
      configManager.setAll({
        provider: 'ollama',
        model: 'llama2',
        baseUrl: 'http://localhost:11434'
      });
      
      expect(configManager.isConfigured()).toBe(true);
    });

    it('should return false when deepseek is missing API key', () => {
      configManager.setAll({
        provider: 'deepseek',
        model: 'deepseek-chat'
        // Missing apiKey
      });
      
      expect(configManager.isConfigured()).toBe(false);
    });

    it('should return false when ollama is missing base URL', () => {
      configManager.setAll({
        provider: 'ollama',
        model: 'llama2'
        // Missing baseUrl
      });
      
      expect(configManager.isConfigured()).toBe(false);
    });
  });

  describe('validateConfig', () => {
    it('should validate valid configuration', () => {
      configManager.setAll({
        provider: 'deepseek',
        model: 'deepseek-chat',
        apiKey: 'sk-test-key',
        maxTokens: 4096,
        temperature: 0.7
      });

      const validation = configManager.validateConfig();
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect missing API key for deepseek', () => {
      configManager.setAll({
        provider: 'deepseek',
        model: 'deepseek-chat'
      });

      const validation = configManager.validateConfig();
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('API key is required for Deepseek provider');
    });

    it('should detect missing base URL for ollama', () => {
      configManager.setAll({
        provider: 'ollama',
        model: 'llama2'
      });

      const validation = configManager.validateConfig();
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Base URL is required for Ollama provider');
    });

    it('should detect invalid temperature', () => {
      // Manually set invalid temperature to test validation
      const config = configManager.getAll();
      (config as any).temperature = 3.0;
      
      // Mock the conf.store to return invalid config
      const mockConf = (configManager as any).conf;
      mockConf.store = config;

      const validation = configManager.validateConfig();
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('getProviderConfig', () => {
    it('should return provider-specific configuration', () => {
      configManager.setAll({
        provider: 'deepseek',
        model: 'deepseek-chat',
        apiKey: 'sk-test-key',
        maxTokens: 2048,
        temperature: 0.8,
        timeout: 30000
      });

      const providerConfig = configManager.getProviderConfig();
      
      expect(providerConfig).toEqual({
        provider: 'deepseek',
        model: 'deepseek-chat',
        apiKey: 'sk-test-key',
        baseUrl: undefined,
        maxTokens: 2048,
        temperature: 0.8,
        timeout: 30000
      });
    });
  });

  describe('reset', () => {
    it('should reset all configuration', () => {
      configManager.setAll({
        provider: 'deepseek',
        model: 'deepseek-chat',
        apiKey: 'sk-test-key'
      });

      expect(configManager.has('apiKey')).toBe(true);
      
      configManager.reset();
      
      expect(configManager.has('apiKey')).toBe(false);
    });
  });

  describe('getConfigPath', () => {
    it('should return configuration file path', () => {
      const path = configManager.getConfigPath();
      expect(typeof path).toBe('string');
      expect(path.length).toBeGreaterThan(0);
    });
  });
});
