import inquirer from 'inquirer';
import chalk from 'chalk';
import { ConfigManager } from '@/core/config';
import { LLMManager } from '@/core/llm-manager';
import { ModernSpinner } from './spinner';
import { Config, ConfigError, ProviderError } from '@/types';

export interface OnboardingResult {
  config: Config;
  success: boolean;
  message: string;
}

export class OnboardingFlow {
  private configManager: ConfigManager;
  private llmManager: LLMManager;

  constructor() {
    this.configManager = ConfigManager.getInstance();
    this.llmManager = new LLMManager();
  }

  public async start(): Promise<OnboardingResult> {
    console.clear();
    this.showWelcome();

    try {
      // Check if already configured
      if (this.configManager.isConfigured()) {
        const shouldReconfigure = await this.askReconfigure();
        if (!shouldReconfigure) {
          return {
            config: this.configManager.getAll(),
            success: true,
            message: 'Using existing configuration'
          };
        }
      }

      // Start configuration flow
      const provider = await this.selectProvider();
      const config = await this.configureProvider(provider);
      
      // Test connection
      await this.testConnection(config);
      
      // Save configuration
      this.configManager.setAll(config);
      
      console.log(chalk.green('\n✅ Configuration completed successfully!'));
      console.log(chalk.cyan('You can now start using Arien AI CLI.\n'));

      return {
        config,
        success: true,
        message: 'Configuration completed successfully'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.log(chalk.red(`\n❌ Configuration failed: ${errorMessage}\n`));
      
      return {
        config: this.configManager.getAll(),
        success: false,
        message: errorMessage
      };
    }
  }

  private showWelcome(): void {
    console.log(chalk.cyan.bold('\n🚀 Welcome to Arien AI CLI!\n'));
    console.log(chalk.white('This powerful CLI tool allows you to interact with AI models'));
    console.log(chalk.white('and execute shell commands through natural language.\n'));
    console.log(chalk.yellow('Let\'s get you set up!\n'));
  }

  private async askReconfigure(): Promise<boolean> {
    const { reconfigure } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'reconfigure',
        message: 'Configuration already exists. Do you want to reconfigure?',
        default: false
      }
    ]);

    return reconfigure;
  }

  private async selectProvider(): Promise<'deepseek' | 'ollama'> {
    console.log(chalk.blue.bold('\n📡 Select your AI Provider:\n'));
    
    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Choose your preferred AI provider:',
        choices: [
          {
            name: `${chalk.green('Deepseek')} - Cloud-based AI with models like deepseek-chat and deepseek-reasoner`,
            value: 'deepseek'
          },
          {
            name: `${chalk.blue('Ollama')} - Local AI models running on your machine`,
            value: 'ollama'
          }
        ]
      }
    ]);

    return provider;
  }

  private async configureProvider(provider: 'deepseek' | 'ollama'): Promise<Partial<Config>> {
    if (provider === 'deepseek') {
      return await this.configureDeepseek();
    } else {
      return await this.configureOllama();
    }
  }

  private async configureDeepseek(): Promise<Partial<Config>> {
    console.log(chalk.blue.bold('\n🔑 Configure Deepseek:\n'));
    console.log(chalk.gray('You can get your API key from: https://platform.deepseek.com/api_keys\n'));

    const questions = [
      {
        type: 'password',
        name: 'apiKey',
        message: 'Enter your Deepseek API key:',
        mask: '*',
        validate: (input: string) => {
          if (!input.trim()) {
            return 'API key is required';
          }
          if (!input.startsWith('sk-')) {
            return 'Deepseek API keys typically start with "sk-"';
          }
          return true;
        }
      },
      {
        type: 'list',
        name: 'model',
        message: 'Select a model:',
        choices: [
          { name: 'deepseek-chat (Recommended for general use)', value: 'deepseek-chat' },
          { name: 'deepseek-reasoner (Better for complex reasoning)', value: 'deepseek-reasoner' }
        ],
        default: 'deepseek-chat'
      },
      {
        type: 'input',
        name: 'baseUrl',
        message: 'Base URL (leave empty for default):',
        default: 'https://api.deepseek.com/v1'
      },
      {
        type: 'number',
        name: 'maxTokens',
        message: 'Maximum tokens per response:',
        default: 4096,
        validate: (input: number) => input > 0 && input <= 32768
      },
      {
        type: 'number',
        name: 'temperature',
        message: 'Temperature (0.0 - 2.0, higher = more creative):',
        default: 0.7,
        validate: (input: number) => input >= 0 && input <= 2
      }
    ];

    const answers = await inquirer.prompt(questions);

    return {
      provider: 'deepseek',
      apiKey: answers.apiKey,
      model: answers.model,
      baseUrl: answers.baseUrl || 'https://api.deepseek.com/v1',
      maxTokens: answers.maxTokens,
      temperature: answers.temperature,
      workingDirectory: process.cwd(),
      autoApprove: false,
      retryAttempts: 3,
      timeout: 30000
    };
  }

  private async configureOllama(): Promise<Partial<Config>> {
    console.log(chalk.blue.bold('\n🏠 Configure Ollama:\n'));
    console.log(chalk.gray('Make sure Ollama is installed and running on your system.\n'));
    console.log(chalk.gray('Install Ollama from: https://ollama.ai\n'));

    const questions = [
      {
        type: 'input',
        name: 'baseUrl',
        message: 'Ollama server URL:',
        default: 'http://localhost:11434',
        validate: (input: string) => {
          if (!input.trim()) {
            return 'Base URL is required';
          }
          try {
            new URL(input);
            return true;
          } catch {
            return 'Please enter a valid URL';
          }
        }
      },
      {
        type: 'input',
        name: 'model',
        message: 'Model name (e.g., llama2, codellama, mistral):',
        default: 'llama2',
        validate: (input: string) => {
          if (!input.trim()) {
            return 'Model name is required';
          }
          return true;
        }
      },
      {
        type: 'number',
        name: 'maxTokens',
        message: 'Maximum tokens per response:',
        default: 4096,
        validate: (input: number) => input > 0 && input <= 32768
      },
      {
        type: 'number',
        name: 'temperature',
        message: 'Temperature (0.0 - 2.0, higher = more creative):',
        default: 0.7,
        validate: (input: number) => input >= 0 && input <= 2
      }
    ];

    const answers = await inquirer.prompt(questions);

    return {
      provider: 'ollama',
      baseUrl: answers.baseUrl,
      model: answers.model,
      maxTokens: answers.maxTokens,
      temperature: answers.temperature,
      workingDirectory: process.cwd(),
      autoApprove: false,
      retryAttempts: 3,
      timeout: 60000 // Longer timeout for local models
    };
  }

  private async testConnection(config: Partial<Config>): Promise<void> {
    const spinner = ModernSpinner.createConnectingSpinner('Testing connection...');
    
    try {
      spinner.start();
      
      // Temporarily set config for testing
      const originalConfig = this.configManager.getAll();
      this.configManager.setAll(config);
      
      // Initialize and test provider
      await this.llmManager.initializeProvider();
      const isConnected = await this.llmManager.validateConnection();
      
      if (!isConnected) {
        throw new ProviderError('Failed to connect to the provider');
      }

      // Test getting available models
      const models = await this.llmManager.getAvailableModels();
      
      spinner.succeed('Connection successful!');
      
      if (models.length > 0) {
        console.log(chalk.green(`✅ Found ${models.length} available models`));
      }
      
      // Restore original config
      this.configManager.setAll(originalConfig);
      
    } catch (error) {
      spinner.fail('Connection failed');
      throw error;
    }
  }

  public async quickSetup(): Promise<OnboardingResult> {
    // Quick setup with defaults for development/testing
    const config: Partial<Config> = {
      provider: 'deepseek',
      model: 'deepseek-chat',
      baseUrl: 'https://api.deepseek.com/v1',
      maxTokens: 4096,
      temperature: 0.7,
      workingDirectory: process.cwd(),
      autoApprove: false,
      retryAttempts: 3,
      timeout: 30000
    };

    // Ask for API key only
    const { apiKey } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: 'Enter your Deepseek API key for quick setup:',
        mask: '*',
        validate: (input: string) => input.trim() ? true : 'API key is required'
      }
    ]);

    config.apiKey = apiKey;

    try {
      await this.testConnection(config);
      this.configManager.setAll(config);
      
      return {
        config: config as Config,
        success: true,
        message: 'Quick setup completed successfully'
      };
    } catch (error) {
      return {
        config: this.configManager.getAll(),
        success: false,
        message: error instanceof Error ? error.message : 'Quick setup failed'
      };
    }
  }
}
