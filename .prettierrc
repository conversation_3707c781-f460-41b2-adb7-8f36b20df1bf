{"semi": true, "trailingComma": "none", "singleQuote": true, "printWidth": 120, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "embeddedLanguageFormatting": "auto", "insertPragma": false, "proseWrap": "preserve", "requirePragma": false, "rangeStart": 0, "rangeEnd": null, "parser": "typescript", "overrides": [{"files": "*.json", "options": {"parser": "json"}}, {"files": "*.md", "options": {"parser": "markdown", "proseWrap": "always", "printWidth": 80}}, {"files": "*.yaml", "options": {"parser": "yaml"}}, {"files": "*.yml", "options": {"parser": "yaml"}}]}