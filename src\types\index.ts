import { z } from 'zod';

// Core Types
export interface LLMProvider {
  name: string;
  models: string[];
  apiKey?: string;
  baseUrl?: string;
  isConfigured: boolean;
}

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCalls?: ToolCall[];
  toolCallId?: string;
  timestamp: Date;
  id: string;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolResult {
  toolCallId: string;
  result: string;
  success: boolean;
  error?: string;
  executionTime: number;
}

export interface ShellCommandResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  command: string;
  executionTime: number;
  success: boolean;
}

// Configuration Schemas
export const ConfigSchema = z.object({
  provider: z.enum(['deepseek', 'ollama']),
  model: z.string(),
  apiKey: z.string().optional(),
  baseUrl: z.string().optional(),
  maxTokens: z.number().default(4096),
  temperature: z.number().min(0).max(2).default(0.7),
  sessionId: z.string().optional(),
  workingDirectory: z.string().optional(),
  autoApprove: z.boolean().default(false),
  retryAttempts: z.number().default(3),
  timeout: z.number().default(30000),
});

export type Config = z.infer<typeof ConfigSchema>;

// Terminal UI Types
export interface TerminalSession {
  id: string;
  name: string;
  messages: LLMMessage[];
  createdAt: Date;
  updatedAt: Date;
  workingDirectory: string;
  provider: string;
  model: string;
}

export interface TerminalState {
  currentSession: TerminalSession | null;
  sessions: TerminalSession[];
  config: Config;
  isProcessing: boolean;
  currentCommand: string;
  messageHistory: LLMMessage[];
}

// Tool Definitions
export interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required: string[];
  };
  usage: {
    when: string;
    whenNot: string;
    parallel: boolean;
    sequential: boolean;
    examples: string[];
  };
}

// Error Types
export class ArienError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ArienError';
  }
}

export class ProviderError extends ArienError {
  constructor(message: string, details?: any) {
    super(message, 'PROVIDER_ERROR', details);
  }
}

export class CommandError extends ArienError {
  constructor(message: string, details?: any) {
    super(message, 'COMMAND_ERROR', details);
  }
}

export class ConfigError extends ArienError {
  constructor(message: string, details?: any) {
    super(message, 'CONFIG_ERROR', details);
  }
}

// Event Types
export interface TerminalEvent {
  type: 'message' | 'command' | 'tool_call' | 'error' | 'config_change';
  data: any;
  timestamp: Date;
}

// Slash Command Types
export interface SlashCommand {
  name: string;
  description: string;
  aliases: string[];
  handler: (args: string[]) => Promise<void>;
  usage: string;
}

export interface ComponentProps {
  state: TerminalState;
  onStateChange: (newState: Partial<TerminalState>) => void;
  onEvent: (event: TerminalEvent) => void;
}
