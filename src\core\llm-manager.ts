import { DeepseekProvider, DeepseekConfig } from '@/providers/deepseek';
import { OllamaProvider, OllamaConfig } from '@/providers/ollama';
import { LLMMessage, ToolDefinition, ProviderError, Config } from '@/types';
import { ConfigManager } from './config';

export type LLMProviderInstance = DeepseekProvider | OllamaProvider;

export class LLMManager {
  private provider: LLMProviderInstance | null = null;
  private configManager: ConfigManager;

  constructor() {
    this.configManager = ConfigManager.getInstance();
  }

  public async initializeProvider(): Promise<void> {
    const config = this.configManager.getAll();
    
    if (config.provider === 'deepseek') {
      if (!config.apiKey) {
        throw new ProviderError('Deepseek API key is required');
      }
      
      const deepseekConfig: DeepseekConfig = {
        apiKey: config.apiKey,
        model: config.model,
        baseUrl: config.baseUrl,
        maxTokens: config.maxTokens,
        temperature: config.temperature,
        timeout: config.timeout
      };
      
      this.provider = new DeepseekProvider(deepseekConfig);
    } else if (config.provider === 'ollama') {
      if (!config.baseUrl) {
        throw new ProviderError('Ollama base URL is required');
      }
      
      const ollamaConfig: OllamaConfig = {
        baseUrl: config.baseUrl,
        model: config.model,
        maxTokens: config.maxTokens,
        temperature: config.temperature,
        timeout: config.timeout
      };
      
      this.provider = new OllamaProvider(ollamaConfig);
    } else {
      throw new ProviderError(`Unsupported provider: ${config.provider}`);
    }

    // Validate connection
    const isConnected = await this.validateConnection();
    if (!isConnected) {
      throw new ProviderError(`Failed to connect to ${config.provider} provider`);
    }
  }

  public async generateResponse(
    messages: LLMMessage[],
    tools?: ToolDefinition[],
    options?: {
      maxTokens?: number;
      temperature?: number;
      stream?: boolean;
    }
  ): Promise<LLMMessage> {
    if (!this.provider) {
      await this.initializeProvider();
    }

    if (!this.provider) {
      throw new ProviderError('No provider initialized');
    }

    return await this.provider.generateResponse(messages, tools, options);
  }

  public async validateConnection(): Promise<boolean> {
    if (!this.provider) {
      return false;
    }

    try {
      return await this.provider.validateConnection();
    } catch (error) {
      console.warn('Provider connection validation failed:', error);
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    if (!this.provider) {
      await this.initializeProvider();
    }

    if (!this.provider) {
      throw new ProviderError('No provider initialized');
    }

    return await this.provider.getAvailableModels();
  }

  public getCurrentProvider(): string {
    const config = this.configManager.getAll();
    return config.provider;
  }

  public getCurrentModel(): string {
    const config = this.configManager.getAll();
    return config.model;
  }

  public async switchProvider(provider: 'deepseek' | 'ollama', model?: string): Promise<void> {
    this.configManager.set('provider', provider);
    
    if (model) {
      this.configManager.set('model', model);
    } else {
      // Set default model for the provider
      const defaultModel = provider === 'deepseek' ? 'deepseek-chat' : 'llama2';
      this.configManager.set('model', defaultModel);
    }

    // Reset provider to force reinitialization
    this.provider = null;
    await this.initializeProvider();
  }

  public async switchModel(model: string): Promise<void> {
    const currentProvider = this.getCurrentProvider();
    
    // Validate model is available
    const availableModels = await this.getAvailableModels();
    if (!availableModels.includes(model)) {
      throw new ProviderError(`Model '${model}' is not available for provider '${currentProvider}'`);
    }

    this.configManager.set('model', model);
    
    // Update provider configuration
    if (this.provider) {
      if (currentProvider === 'deepseek' && this.provider instanceof DeepseekProvider) {
        this.provider.updateConfig({ model });
      } else if (currentProvider === 'ollama' && this.provider instanceof OllamaProvider) {
        this.provider.updateConfig({ model });
      }
    }
  }

  public updateProviderConfig(config: Partial<DeepseekConfig | OllamaConfig>): void {
    if (!this.provider) {
      throw new ProviderError('No provider initialized');
    }

    const currentProvider = this.getCurrentProvider();
    
    if (currentProvider === 'deepseek' && this.provider instanceof DeepseekProvider) {
      this.provider.updateConfig(config as Partial<DeepseekConfig>);
      
      // Update config manager
      if ('apiKey' in config && config.apiKey) {
        this.configManager.set('apiKey', config.apiKey);
      }
      if ('baseUrl' in config && config.baseUrl) {
        this.configManager.set('baseUrl', config.baseUrl);
      }
    } else if (currentProvider === 'ollama' && this.provider instanceof OllamaProvider) {
      this.provider.updateConfig(config as Partial<OllamaConfig>);
      
      // Update config manager
      if ('baseUrl' in config && config.baseUrl) {
        this.configManager.set('baseUrl', config.baseUrl);
      }
    }

    // Update common config
    if ('maxTokens' in config && config.maxTokens) {
      this.configManager.set('maxTokens', config.maxTokens);
    }
    if ('temperature' in config && config.temperature !== undefined) {
      this.configManager.set('temperature', config.temperature);
    }
    if ('timeout' in config && config.timeout) {
      this.configManager.set('timeout', config.timeout);
    }
  }

  public getProviderConfig(): DeepseekConfig | OllamaConfig | null {
    if (!this.provider) {
      return null;
    }

    return this.provider.getConfig();
  }

  public isInitialized(): boolean {
    return this.provider !== null;
  }

  public async reinitialize(): Promise<void> {
    this.provider = null;
    await this.initializeProvider();
  }

  // Ollama-specific methods
  public async pullModel(modelName: string): Promise<void> {
    if (!this.provider || !(this.provider instanceof OllamaProvider)) {
      throw new ProviderError('Ollama provider is required for pulling models');
    }

    await this.provider.pullModel(modelName);
  }

  public async deleteModel(modelName: string): Promise<void> {
    if (!this.provider || !(this.provider instanceof OllamaProvider)) {
      throw new ProviderError('Ollama provider is required for deleting models');
    }

    await this.provider.deleteModel(modelName);
  }

  public async getModelInfo(modelName: string): Promise<any> {
    if (!this.provider || !(this.provider instanceof OllamaProvider)) {
      throw new ProviderError('Ollama provider is required for getting model info');
    }

    return await this.provider.getModelInfo(modelName);
  }
}
